import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import {
  MJPEGStreamClient,
  SonarMetadata,
  FrameData,
  StreamStatus,
  createImageUrl,
  extractSonarMetadata,
} from '../mjpeg-stream';

// 连接状态类型
export type ConnectionStatus = "standby" | "connecting" | "connected" | "paused";

// Hook选项接口
export interface UseConnectionManagerOptions {
  sonarIP: string;
  streamEndpoint?: string;
  debug?: boolean;
  autoStart?: boolean;
  onError?: (error: Error) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onMetaData?: (metadata: SonarMetadata) => void;
  onFrame?: (frameData: FrameData<SonarMetadata>) => void;
}

// Hook返回类型
export interface UseConnectionManagerReturn {
  connectionStatus: ConnectionStatus;
  error: string | null;
  currentImageSrc: string | null;
  pausedImageSrc: string | null;
  currentSonarMetadata: SonarMetadata["SonarParams"] | null;
  frameCount: number;
  streamUrl: string;
  
  // 控制函数
  handleImageClick: () => void;
  handlePause: () => void;
  handleResumeFromPause: () => void;
  handleStopFromPause: () => void;
  
  // 客户端引用
  streamClientRef: React.MutableRefObject<MJPEGStreamClient<SonarMetadata> | null>;
}

/**
 * 连接管理Hook
 * 负责MJPEG流连接状态管理和控制
 */
export const useConnectionManager = (options: UseConnectionManagerOptions): UseConnectionManagerReturn => {
  const {
    sonarIP,
    streamEndpoint = "/stream",
    debug = false,
    autoStart = false,
    onError,
    onConnect,
    onDisconnect,
    onMetaData,
    onFrame,
  } = options;

  // 状态定义
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>("standby");
  const [error, setError] = useState<string | null>(null);
  const [currentImageSrc, setCurrentImageSrc] = useState<string | null>(null);
  const [pausedImageSrc, setPausedImageSrc] = useState<string | null>(null);
  const [currentSonarMetadata, setCurrentSonarMetadata] = useState<SonarMetadata["SonarParams"] | null>(null);

  // 引用
  const streamClientRef = useRef<MJPEGStreamClient<SonarMetadata> | null>(null);
  const cleanupRef = useRef<(() => void) | null>(null);
  const frameCountRef = useRef<number>(0);
  const callbacksRef = useRef({
    onConnect,
    onDisconnect,
    onError,
    onMetaData,
    onFrame,
  });

  // 构造流URL
  const streamUrl = useMemo(() => {
    if (sonarIP.startsWith('http://') || sonarIP.startsWith('https://')) {
      return `${sonarIP}${streamEndpoint}`;
    }
    return `http://${sonarIP}${streamEndpoint}`;
  }, [sonarIP, streamEndpoint]);

  // 更新回调函数引用
  useEffect(() => {
    callbacksRef.current = {
      onConnect,
      onDisconnect,
      onError,
      onMetaData,
      onFrame,
    };
  }, [onConnect, onDisconnect, onError, onMetaData, onFrame]);

  // 客户端回调函数
  const clientCallbacks = useMemo(() => ({
    onConnect: () => {
      if (callbacksRef.current.onConnect) callbacksRef.current.onConnect();
    },
    onDisconnect: () => {
      if (callbacksRef.current.onDisconnect) callbacksRef.current.onDisconnect();
    },
    onError: (err: Error) => {
      if (callbacksRef.current.onError) callbacksRef.current.onError(err);
    },
    onFrame: (frameData: FrameData<SonarMetadata>) => {
      console.log(`🖼️ [useConnectionManager] 收到帧数据，大小: ${frameData.frame.length} bytes`);
      const url = createImageUrl(frameData.frame);
      console.log(`🔗 [useConnectionManager] 创建图像URL: ${url.substring(0, 50)}...`);
      setCurrentImageSrc(url);

      frameCountRef.current += 1;

      // 保存当前声呐元数据
      if (frameData.metadata.SonarParams) {
        console.log(`📊 [useConnectionManager] 收到声呐元数据:`, frameData.metadata.SonarParams);
        setCurrentSonarMetadata(frameData.metadata.SonarParams);
      }

      // 调用外部onFrame回调
      if (callbacksRef.current.onFrame) {
        callbacksRef.current.onFrame(frameData);
      }
    },
    onMetadata: (metadata: SonarMetadata) => {
      if (metadata.SonarParams) {
        if (callbacksRef.current.onMetaData) callbacksRef.current.onMetaData(metadata);
      }
    },
  }), []);

  // 创建流客户端
  useEffect(() => {
    const componentId = Math.random().toString(36).substring(2, 11);
    console.log(`🏗️ [useConnectionManager-${componentId}] 创建客户端`);
    
    if (streamClientRef.current) {
      console.log(`⚠️ [useConnectionManager-${componentId}] 客户端已存在，跳过创建`);
      return;
    }

    console.log(`🆕 [useConnectionManager-${componentId}] 创建新的 MJPEGStreamClient`);
    const client = new MJPEGStreamClient<SonarMetadata>(
      {
        streamUrl,
        debug,
        metadataExtractor: extractSonarMetadata,
        autoStart: false,
      },
      clientCallbacks
    );

    streamClientRef.current = client;

    // 订阅客户端状态变化
    const unsubscribe = client.subscribe((status, errorMsg) => {
      const componentStatus = status === StreamStatus.STANDBY ? "standby" 
        : status === StreamStatus.CONNECTING ? "connecting" 
        : "connected";
      
      setConnectionStatus(componentStatus);
      setError(errorMsg || null);
      
      if (componentStatus !== "connected") {
        setCurrentImageSrc(null);
      }
    });

    // 保存清理函数
    cleanupRef.current = () => {
      console.log(`🧹 [useConnectionManager-${componentId}] 清理客户端`);
      unsubscribe();
      client.stop();
    };

    return () => {
      console.log(`🔄 [useConnectionManager-${componentId}] useEffect 清理回调执行`);
      if (cleanupRef.current) {
        cleanupRef.current();
        cleanupRef.current = null;
      }
      streamClientRef.current = null;
    };
  }, [clientCallbacks, debug, streamUrl]);

  // 更新客户端配置
  useEffect(() => {
    console.log(`⚙️ [useConnectionManager] 配置更新，url=${streamUrl}, debug=${debug}`);
    if (streamClientRef.current) {
      console.log(`🔧 [useConnectionManager] 更新客户端配置`);
      streamClientRef.current.updateOptions({
        streamUrl,
        debug,
        metadataExtractor: extractSonarMetadata,
      });
    }
  }, [streamUrl, debug]);

  // 控制连接启动/停止
  useEffect(() => {
    console.log(`🎛️ [useConnectionManager] autoStart 控制，autoStart=${autoStart}`);
    if (streamClientRef.current && autoStart) {
      console.log(`▶️ [useConnectionManager] 启动客户端连接`);
      streamClientRef.current.start();
    }
  }, [autoStart]);

  // 控制函数
  const handleImageClick = useCallback(() => {
    if (streamClientRef.current) {
      const currentStatus = streamClientRef.current.status;
      if (currentStatus === StreamStatus.CONNECTING) {
        streamClientRef.current.stop();
      } else {
        streamClientRef.current.toggle();
      }
    }
  }, []);

  const handlePause = useCallback(() => {
    if (connectionStatus === "connected" && currentImageSrc) {
      setPausedImageSrc(currentImageSrc);
      if (streamClientRef.current) {
        streamClientRef.current.stop();
      }
      setConnectionStatus("paused");
      console.log('📸 [useConnectionManager] 暂停连接，保存最后一帧图像');
    }
  }, [connectionStatus, currentImageSrc]);

  const handleResumeFromPause = useCallback(() => {
    if (connectionStatus === "paused") {
      setPausedImageSrc(null);
      if (streamClientRef.current) {
        streamClientRef.current.start();
      }
      console.log('▶️ [useConnectionManager] 从暂停状态恢复连接');
    }
  }, [connectionStatus]);

  const handleStopFromPause = useCallback(() => {
    if (connectionStatus === "paused") {
      setPausedImageSrc(null);
      setConnectionStatus("standby");
      console.log('⏹️ [useConnectionManager] 从暂停状态返回待机');
    }
  }, [connectionStatus]);

  return {
    connectionStatus,
    error,
    currentImageSrc,
    pausedImageSrc,
    currentSonarMetadata,
    frameCount: frameCountRef.current,
    streamUrl,
    
    handleImageClick,
    handlePause,
    handleResumeFromPause,
    handleStopFromPause,
    
    streamClientRef,
  };
};