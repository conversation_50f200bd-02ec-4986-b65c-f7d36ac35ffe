import { useState, useEffect, useCallback, useRef } from 'react';
import { SonarMetadata } from '../mjpeg-stream';

// Hook选项接口
export interface UseImageMetricsOptions {
  currentSonarMetadata: SonarMetadata["SonarParams"] | null;
}

// Hook返回类型
export interface UseImageMetricsReturn {
  imageMetrics: {width: number, height: number, range: number} | null;
  containerSize: {width: number, height: number};
  containerRef: React.RefObject<HTMLDivElement | null>;
  canvasRef: React.RefObject<HTMLCanvasElement | null>;
  
  // 控制函数
  updateContainerSize: () => void;
}

/**
 * 图像尺寸管理Hook
 * 负责图像尺寸、容器尺寸和量程数据的管理
 */
export const useImageMetrics = (options: UseImageMetricsOptions): UseImageMetricsReturn => {
  const { currentSonarMetadata } = options;

  // 状态定义
  const [imageMetrics, setImageMetrics] = useState<{width: number, height: number, range: number} | null>(null);
  const [containerSize, setContainerSize] = useState<{width: number, height: number}>({width: 0, height: 0});

  // 引用
  const containerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // 容器尺寸更新函数
  const updateContainerSize = useCallback(() => {
    const container = containerRef.current;
    if (container) {
      const rect = container.getBoundingClientRect();
      setContainerSize({
        width: rect.width,
        height: rect.height,
      });
    }
  }, []);

  // 监听容器尺寸变化
  useEffect(() => {
    updateContainerSize();
    
    const container = containerRef.current;
    if (!container) return;
    
    const resizeObserver = new ResizeObserver(() => {
      updateContainerSize();
    });
    resizeObserver.observe(container);
    
    return () => {
      resizeObserver.disconnect();
    };
  }, [updateContainerSize]);

  // 监听canvas尺寸变化
  useEffect(() => {
    const canvas = canvasRef.current;
    console.log(`📏 [useImageMetrics] 更新检查:`, {
      hasCanvas: !!canvas,
      hasSonarMetadata: !!currentSonarMetadata,
      canvasWidth: canvas?.width,
      canvasHeight: canvas?.height,
      sonarMetadata: currentSonarMetadata
    });

    if (!canvas || !currentSonarMetadata) {
      console.log(`📏 [useImageMetrics] 设置imageMetrics为null`);
      setImageMetrics(null);
      return;
    }

    const updateMetrics = () => {
      const metrics = {
        width: canvas.width || 640,
        height: canvas.height || 480,
        range: currentSonarMetadata.range || 50,
      };
      console.log(`📏 [useImageMetrics] 更新imageMetrics:`, metrics);
      setImageMetrics(metrics);
    };

    // 初始更新
    updateMetrics();

    // 使用ResizeObserver监听canvas尺寸变化
    const resizeObserver = new ResizeObserver(() => {
      updateMetrics();
    });

    resizeObserver.observe(canvas);

    return () => {
      resizeObserver.disconnect();
    };
  }, [currentSonarMetadata]);

  return {
    imageMetrics,
    containerSize,
    containerRef,
    canvasRef,
    
    updateContainerSize,
  };
};