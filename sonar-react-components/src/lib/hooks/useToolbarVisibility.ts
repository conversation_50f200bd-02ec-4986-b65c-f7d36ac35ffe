import { useState, useCallback, useRef, useEffect } from 'react';

// Hook选项接口
export interface UseToolbarVisibilityOptions {
  isRecording?: boolean;
}

// Hook返回类型
export interface UseToolbarVisibilityReturn {
  isMouseIdle: boolean;
  showRecordingToolbar: boolean;
  
  // 事件处理函数
  handleMouseMove: () => void;
  handleToolbarMouseEnter: () => void;
  handleToolbarMouseLeave: () => void;
}

/**
 * 工具栏可见性管理Hook
 * 负责鼠标空闲检测和工具栏显示/隐藏逻辑
 */
export const useToolbarVisibility = (options: UseToolbarVisibilityOptions): UseToolbarVisibilityReturn => {
  const { isRecording = false } = options;

  // 状态定义
  const [isMouseIdle, setIsMouseIdle] = useState<boolean>(false);
  const [showRecordingToolbar, setShowRecordingToolbar] = useState<boolean>(false);

  // 定时器引用
  const mouseIdleTimerRef = useRef<NodeJS.Timeout | null>(null);
  const toolbarHideTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 鼠标移动处理函数
  const handleMouseMove = useCallback(() => {
    setIsMouseIdle(false);
    
    // 清除之前的定时器
    if (mouseIdleTimerRef.current) {
      clearTimeout(mouseIdleTimerRef.current);
    }
    
    // 设置新的定时器，2秒后隐藏鼠标
    mouseIdleTimerRef.current = setTimeout(() => {
      setIsMouseIdle(true);
    }, 2000);
  }, []);

  // 工具栏鼠标进入处理函数
  const handleToolbarMouseEnter = useCallback(() => {
    setShowRecordingToolbar(true);
    
    // 清除隐藏定时器
    if (toolbarHideTimerRef.current) {
      clearTimeout(toolbarHideTimerRef.current);
      toolbarHideTimerRef.current = null;
    }
  }, []);

  // 工具栏鼠标离开处理函数
  const handleToolbarMouseLeave = useCallback(() => {
    // 如果正在录制，不自动隐藏工具栏
    if (isRecording) {
      return;
    }
    
    // 延迟隐藏工具栏
    toolbarHideTimerRef.current = setTimeout(() => {
      setShowRecordingToolbar(false);
    }, 1000);
  }, [isRecording]);

  // 录制状态变化时管理工具栏显示
  useEffect(() => {
    if (isRecording) {
      // 开始录制时显示工具栏
      setShowRecordingToolbar(true);
      // 清除隐藏定时器
      if (toolbarHideTimerRef.current) {
        clearTimeout(toolbarHideTimerRef.current);
        toolbarHideTimerRef.current = null;
      }
    }
  }, [isRecording]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (mouseIdleTimerRef.current) {
        clearTimeout(mouseIdleTimerRef.current);
        mouseIdleTimerRef.current = null;
      }
      if (toolbarHideTimerRef.current) {
        clearTimeout(toolbarHideTimerRef.current);
        toolbarHideTimerRef.current = null;
      }
    };
  }, []);

  return {
    isMouseIdle,
    showRecordingToolbar,
    
    handleMouseMove,
    handleToolbarMouseEnter,
    handleToolbarMouseLeave,
  };
};