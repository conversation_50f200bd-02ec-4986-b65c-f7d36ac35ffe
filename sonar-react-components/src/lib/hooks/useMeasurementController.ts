import { useState, useCallback, useMemo } from 'react';
import { MeasurementState, MeasurementMode } from '../../components/measurement';
import { SonarMeasurementConfig } from '../sonar-geometry';

// Hook选项接口
export interface UseMeasurementControllerOptions {
  enableMeasurement?: boolean;
  measurementConfig?: SonarMeasurementConfig;
}

// Hook返回类型
export interface UseMeasurementControllerReturn {
  measurementState: MeasurementState;
  setMeasurementState: React.Dispatch<React.SetStateAction<MeasurementState>>;
  showMeasurementToolbar: boolean;
  finalMeasurementConfig: SonarMeasurementConfig;
  
  // 控制函数
  handleMeasurementModeChange: (mode: MeasurementMode) => void;
  handleClearMeasurements: () => void;
  handleToggleMeasurementToolbar: () => void;
}

// 默认测量配置
const defaultMeasurementConfig: SonarMeasurementConfig = {
  enabled: true,
  originConfig: {
    preset: 'bottom-center',
  },
  sonarAngle: 120,
  sonarDirection: 270,
  showOriginMarker: false,
  showSectorOutline: false,
  coordinateFormat: 'polar',
  precision: {
    distance: 2,
    angle: 1,
  },
};

/**
 * 测量控制Hook
 * 负责测量功能状态管理和控制逻辑
 */
export const useMeasurementController = (options: UseMeasurementControllerOptions): UseMeasurementControllerReturn => {
  const {
    measurementConfig,
  } = options;

  // 测量功能状态
  const [measurementState, setMeasurementState] = useState<MeasurementState>({
    mode: 'none',
    isActive: false,
    points: [],
    results: [],
    isDragging: false,
  });

  const [showMeasurementToolbar, setShowMeasurementToolbar] = useState<boolean>(false);

  // 合并的测量配置
  const finalMeasurementConfig = useMemo(() => {
    return { ...defaultMeasurementConfig, ...measurementConfig };
  }, [measurementConfig]);

  // 测量模式控制
  const handleMeasurementModeChange = useCallback((mode: MeasurementMode) => {
    setMeasurementState(prev => ({
      ...prev,
      mode,
      isActive: mode !== 'none',
      points: mode === 'none' ? [] : prev.points,
      results: mode === 'none' ? [] : prev.results,
    }));
  }, []);

  // 清除测量结果
  const handleClearMeasurements = useCallback(() => {
    setMeasurementState(prev => ({
      ...prev,
      points: [],
      results: [],
    }));
  }, []);

  // 切换工具栏显示
  const handleToggleMeasurementToolbar = useCallback(() => {
    setShowMeasurementToolbar(prev => !prev);
  }, []);

  return {
    measurementState,
    setMeasurementState,
    showMeasurementToolbar,
    finalMeasurementConfig,
    
    handleMeasurementModeChange,
    handleClearMeasurements,
    handleToggleMeasurementToolbar,
  };
};