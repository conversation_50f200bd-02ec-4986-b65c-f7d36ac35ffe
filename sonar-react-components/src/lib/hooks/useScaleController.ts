import { useState, useCallback, useMemo } from 'react';

// 缩放模式类型
export type ScaleMode = 'fit' | 'original';

// 缩放配置接口
export interface ScaleConfig {
  mode: ScaleMode;
  allowModeToggle?: boolean;
}

// Hook选项接口
export interface UseScaleControllerOptions {
  scaleConfig?: ScaleConfig;
  imageMetrics?: {width: number, height: number, range: number} | null;
  containerSize: {width: number, height: number};
}

// Hook返回类型
export interface UseScaleControllerReturn {
  currentScaleMode: ScaleMode;
  finalScaleConfig: ScaleConfig;
  scaleCalculation: {
    scale: number;
    transformOrigin: string;
  };
  
  // 控制函数
  handleScaleModeChange: () => void;
}

// 默认缩放配置
const defaultScaleConfig: ScaleConfig = {
  mode: 'fit',
  allowModeToggle: true,
};

/**
 * 缩放控制Hook
 * 负责图像缩放模式管理和缩放计算
 */
export const useScaleController = (options: UseScaleControllerOptions): UseScaleControllerReturn => {
  const {
    scaleConfig,
    imageMetrics,
    containerSize,
  } = options;

  // 合并的缩放配置
  const finalScaleConfig = useMemo(() => {
    return { ...defaultScaleConfig, ...scaleConfig };
  }, [scaleConfig]);

  // 缩放功能状态
  const [currentScaleMode, setCurrentScaleMode] = useState<ScaleMode>(() => 
    finalScaleConfig?.mode || defaultScaleConfig.mode
  );

  // 缩放模式切换
  const handleScaleModeChange = useCallback(() => {
    if (finalScaleConfig.allowModeToggle) {
      setCurrentScaleMode(prev => prev === 'fit' ? 'original' : 'fit');
    }
  }, [finalScaleConfig.allowModeToggle]);

  // 缩放计算逻辑
  const scaleCalculation = useMemo(() => {
    if (!imageMetrics || containerSize.width === 0 || containerSize.height === 0) {
      return {
        scale: 1,
        transformOrigin: 'center center',
      };
    }
    
    const { width: imageWidth, height: imageHeight } = imageMetrics;
    const { width: containerWidth, height: containerHeight } = containerSize;
    
    // 计算可能的缩放值
    const scaleX = containerWidth / imageWidth;
    const scaleY = containerHeight / imageHeight;
    
    if (currentScaleMode === 'fit') {
      // 适应模式：自动缩放以适应容器大小，完整显示所有内容
      const scale = Math.min(scaleX, scaleY); // 使用较小的缩放值确保完整显示
      
      return {
        scale,
        transformOrigin: 'center center',
      };
    } else {
      // 原始模式：保持图像1:1像素对应，不放大但可缩小以适应容器
      const scale = Math.min(1, Math.min(scaleX, scaleY)); // 不超过原始尺寸，也不超过容器
      
      return {
        scale,
        transformOrigin: 'center center',
      };
    }
  }, [imageMetrics, containerSize, currentScaleMode]);

  return {
    currentScaleMode,
    finalScaleConfig,
    scaleCalculation,
    
    handleScaleModeChange,
  };
};