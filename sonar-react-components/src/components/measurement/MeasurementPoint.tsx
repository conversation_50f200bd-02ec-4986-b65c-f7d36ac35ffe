import React, { useCallback, useState } from 'react';
import { MeasurementPointProps } from './types';
import CoordinateLabel from './CoordinateLabel';

/**
 * 测量点组件
 * 显示测量点并处理用户交互（选择、拖拽等）
 */
const MeasurementPoint: React.FC<MeasurementPointProps> = ({
  point,
  isSelected,
  isDraggable = true,
  onSelect,
  onDrag,
  onDragStart,
  onDragEnd,
  showCoordinates = true,
  coordinateFormat = 'polar',
  className = '',
}) => {
  const [isDragging, setIsDragging] = useState(false);

  // 处理点击选择
  const handleClick = useCallback((event: React.MouseEvent) => {
    event.stopPropagation();
    if (onSelect && !isDragging) {
      onSelect(point);
    }
  }, [onSelect, point, isDragging]);

  // 处理拖拽开始
  const handlePointerDown = useCallback((event: React.PointerEvent) => {
    if (!isDraggable) return;

    event.stopPropagation();
    event.preventDefault();

    setIsDragging(true);

    if (onDragStart) {
      onDragStart(point);
    }

    // 捕获指针以处理移动到元素外的情况
    event.currentTarget.setPointerCapture(event.pointerId);
  }, [isDraggable, point, onDragStart]);

  // 处理拖拽移动
  const handlePointerMove = useCallback((event: React.PointerEvent) => {
    if (!isDragging || !onDrag) return;

    event.preventDefault();

    // 获取相对于父容器的坐标
    const target = event.currentTarget as HTMLElement;
    const container = target.offsetParent as HTMLElement;
    if (container) {
      const containerRect = container.getBoundingClientRect();
      // 计算鼠标在容器中的位置（视口坐标）
      const mouseX = event.clientX - containerRect.left;
      const mouseY = event.clientY - containerRect.top;

      // 直接使用鼠标位置作为新的图像坐标，不减去dragOffset
      // 这样测量点会直接跟随鼠标位置，而不是保持初始点击时的偏移
      onDrag(point, { x: mouseX, y: mouseY });
    }
  }, [isDragging, onDrag, point]);

  // 处理拖拽结束
  const handlePointerUp = useCallback((event: React.PointerEvent) => {
    if (!isDragging) return;
    
    event.preventDefault();
    setIsDragging(false);
    
    if (onDragEnd) {
      onDragEnd(point);
    }
    
    // 释放指针捕获
    event.currentTarget.releasePointerCapture(event.pointerId);
  }, [isDragging, point, onDragEnd]);

  // 根据点的有效性选择颜色
  const getPointColor = () => {
    if (!point.isValid) return '#ef4444'; // red-500
    if (isSelected) return '#3b82f6'; // blue-500
    return '#10b981'; // green-500
  };

  const pointColor = getPointColor();
  const iconSize = isSelected ? 24 : 20;

  return (
    <>
      {/* 测量点标记 */}
      <div
        className={`absolute z-50 cursor-pointer select-none transition-all duration-200 ${className} ${
          isDraggable ? 'hover:scale-110' : ''
        } ${isDragging ? 'scale-125 z-30' : ''}`}
        style={{
          left: point.pixelCoordinate.x - iconSize / 2,
          top: point.pixelCoordinate.y - iconSize / 2,
          width: iconSize,
          height: iconSize,
        }}
        onClick={handleClick}
        onPointerDown={handlePointerDown}
        onPointerMove={handlePointerMove}
        onPointerUp={handlePointerUp}
        onPointerLeave={handlePointerUp} // 处理指针离开的情况
      >
        {/* 背景十字线 */}
        <div className="absolute inset-0 flex items-center justify-center transition-all duration-200">
          {/* 水平线 */}
          <div
            className="absolute h-px"
            style={{
              width: iconSize * 0.6,
              backgroundColor: pointColor,
              opacity: 0.8,
            }}
          />
          {/* 垂直线 */}
          <div
            className="absolute w-px"
            style={{
              height: iconSize * 0.6,
              backgroundColor: pointColor,
              opacity: 0.8,
            }}
          />
        </div>
        
        {/* 选中状态的动画描边圆圈 */}
        {isSelected ? (
          <div
            className="absolute inset-0 rounded-full border-2 animate-pulse"
            style={{
              borderColor: pointColor,
              backgroundColor: 'transparent',
              transform: 'scale(1.2)',
            }}
          />
        ) : null}
        
        {/* 中心小圆点 */}
        <div
          className="absolute rounded-full"
          style={{
            left: '50%',
            top: '50%',
            width: 4,
            height: 4,
            backgroundColor: pointColor,
            transform: 'translate(-50%, -50%)',
            boxShadow: '0 0 4px rgba(0,0,0,0.5)',
          }}
        />
      </div>

      {/* 坐标标注 */}
      {showCoordinates && (
        <CoordinateLabel
          point={point}
          format={coordinateFormat}
          precision={{ distance: 2, angle: 1 }}
          visible={isSelected || isDragging}
          position="auto"
        />
      )}
      
      {/* 拖拽时的辅助线（可选） */}
      {isDragging && (
        <div
          className="absolute pointer-events-none z-10"
          style={{
            left: 0,
            top: 0,
            width: '100%',
            height: '100%',
          }}
        >
          {/* 水平辅助线 */}
          <div
            className="absolute h-px bg-blue-400/50"
            style={{
              left: 0,
              top: point.pixelCoordinate.y,
              width: '100%',
            }}
          />
          {/* 垂直辅助线 */}
          <div
            className="absolute w-px bg-blue-400/50"
            style={{
              left: point.pixelCoordinate.x,
              top: 0,
              height: '100%',
            }}
          />
        </div>
      )}
    </>
  );
};

export default MeasurementPoint;