import React, { useCallback } from 'react';
import { 
  Circle, 
  Square, 
  Download, 
  Power, 
  Ruler, 
  CirclePause, 
  Play, 
  Maximize2, 
  Minimize2 
} from 'lucide-react';
import { ConnectionStatus } from '../../lib/hooks/useConnectionManager';
import { VideoRecorderHook } from '../../lib/hooks/useVideoRecorder';
import { ScaleMode, ScaleConfig } from '../../lib/hooks/useScaleController';

// 组件属性接口
export interface RecordingToolbarProps {
  connectionStatus: ConnectionStatus;
  videoRecorder: VideoRecorderHook;
  recordedBlob: Blob | null;
  
  // 控制功能
  enableMeasurement: boolean;
  showMeasurementToolbar: boolean;
  currentScaleMode: ScaleMode;
  finalScaleConfig: ScaleConfig;
  
  // 事件处理函数
  onPause: () => void;
  onResumeFromPause: () => void;
  onStopFromPause: () => void;
  onImageClick: () => void;
  onStartRecording: () => void;
  onStopRecording: () => void;
  onDownloadRecording: () => void;
  onToggleMeasurementToolbar: () => void;
  onScaleModeChange: () => void;
  
  // 格式化函数
  formatRecordingTime: (seconds: number) => string;
}

/**
 * 录制工具栏组件
 * 包含录制、连接控制、测量工具等按钮
 */
export const RecordingToolbar: React.FC<RecordingToolbarProps> = ({
  connectionStatus,
  videoRecorder,
  recordedBlob,
  enableMeasurement,
  showMeasurementToolbar,
  currentScaleMode,
  finalScaleConfig,
  onPause,
  onResumeFromPause,
  onStopFromPause,
  onImageClick,
  onStartRecording,
  onStopRecording,
  onDownloadRecording,
  onToggleMeasurementToolbar,
  onScaleModeChange,
  formatRecordingTime,
}) => {
  // 阻止事件冒泡的包装函数
  const createStopPropagationHandler = useCallback((handler: () => void) => {
    return (e: React.MouseEvent) => {
      e.stopPropagation();
      e.preventDefault();
      handler();
    };
  }, []);

  return (
    <>
      {/* 连接控制按钮 */}
      {connectionStatus === "connected" && (
        <>
          {/* 暂停按钮 */}
          <button
            onClick={createStopPropagationHandler(onPause)}
            className="bg-yellow-600 hover:bg-yellow-700 text-white border border-yellow-500 rounded p-2 transition-colors"
            title="暂停"
          >
            <CirclePause size={16} />
          </button>
          {/* 断开连接按钮 */}
          <button
            onClick={createStopPropagationHandler(onImageClick)}
            className="bg-gray-600 hover:bg-gray-700 text-white border border-gray-500 rounded p-2 transition-colors"
            title="断开连接"
          >
            <Power size={16} />
          </button>
        </>
      )}

      {connectionStatus === "paused" && (
        <>
          {/* 恢复按钮 */}
          <button
            onClick={createStopPropagationHandler(onResumeFromPause)}
            className="bg-green-600 hover:bg-green-700 text-white border border-green-500 rounded p-2 transition-colors"
            title="恢复连接"
          >
            <Play size={16} />
          </button>
          {/* 停止按钮 */}
          <button
            onClick={createStopPropagationHandler(onStopFromPause)}
            className="bg-gray-600 hover:bg-gray-700 text-white border border-gray-500 rounded p-2 transition-colors"
            title="停止"
          >
            <Power size={16} />
          </button>
        </>
      )}

      {/* 缩放模式切换按钮 */}
      {finalScaleConfig.allowModeToggle && (
        <button
          onClick={createStopPropagationHandler(onScaleModeChange)}
          className="bg-gray-600 hover:bg-gray-700 text-white border border-gray-500 rounded p-2 transition-colors"
          title={`切换到${currentScaleMode === 'fit' ? '原始尺寸' : '适应'}模式`}
        >
          {currentScaleMode === 'fit' ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
        </button>
      )}

      {/* 测量工具按钮 */}
      {enableMeasurement && (
        <button
          onClick={createStopPropagationHandler(onToggleMeasurementToolbar)}
          className={`border rounded p-2 transition-colors ${
            showMeasurementToolbar 
              ? 'bg-blue-600 hover:bg-blue-700 text-white border-blue-500' 
              : 'bg-gray-600 hover:bg-gray-700 text-white border-gray-500'
          }`}
          title="测量工具"
        >
          <Ruler size={16} />
        </button>
      )}
      
      {/* 录制按钮组 */}
      {videoRecorder.isSupported && (
        <>
          {!videoRecorder.isRecording ? (
            <button
              onClick={createStopPropagationHandler(onStartRecording)}
              className="bg-red-600 hover:bg-red-700 text-white border border-red-500 rounded p-2 transition-colors"
              title="开始录制"
            >
              <Circle size={16} />
            </button>
          ) : (
            <button
              onClick={createStopPropagationHandler(onStopRecording)}
              className={`bg-red-700 text-white border border-red-600 rounded p-2 transition-colors ${
                videoRecorder.isPaused ? '' : 'animate-pulse'
              }`}
              title={`停止录制 (${formatRecordingTime(videoRecorder.duration)})`}
            >
              <Square size={16} />
            </button>
          )}
          
          {recordedBlob && (
            <button
              onClick={createStopPropagationHandler(onDownloadRecording)}
              className="bg-green-600 hover:bg-green-700 text-white border border-green-500 rounded p-2 transition-colors"
              title="下载录制文件"
            >
              <Download size={16} />
            </button>
          )}

          {/* 录制状态指示器 */}
          {videoRecorder.isRecording && (
            <div className="flex items-center gap-2 ml-2 px-2 py-1 text-xs text-white rounded bg-red-900/50 whitespace-nowrap">
              <div className={`w-2 h-2 bg-red-500 rounded-full ${
                videoRecorder.isPaused ? '' : 'animate-pulse'
              }`}></div>
              <span>
                {videoRecorder.isPaused ? '录制暂停' : '录制中'} {formatRecordingTime(videoRecorder.duration)}
              </span>
            </div>
          )}
        </>
      )}
    </>
  );
};