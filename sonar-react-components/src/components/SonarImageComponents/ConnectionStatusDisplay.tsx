import React from 'react';
import { ConnectionStatus } from '../../lib/hooks/useConnectionManager';

// 组件属性接口
export interface ConnectionStatusDisplayProps {
  connectionStatus: ConnectionStatus;
  error: string | null;
  themeStyles: {
    standby: string;
    connecting: string;
    error: string;
  };
}

/**
 * 连接状态显示组件
 * 负责显示待机、连接中、错误等状态的UI
 */
export const ConnectionStatusDisplay: React.FC<ConnectionStatusDisplayProps> = ({
  connectionStatus,
  error,
  themeStyles,
}) => {
  switch (connectionStatus) {
    case "standby":
      return (
        <div className={`flex flex-col justify-center items-center w-full h-full text-center p-5 ${themeStyles.standby}`}>
          <p>点击连接MJPEG流</p>
          <p className="mt-2 text-sm opacity-70">或按空格键</p>
          {error && <p className={`mt-2 text-sm ${themeStyles.error}`}>{error}</p>}
        </div>
      );
    
    case "connecting":
      return (
        <div className={`flex flex-col justify-center items-center w-full h-full text-center p-5 ${themeStyles.connecting}`}>
          <p>正在连接...</p>
          <div className="mt-4 w-8 h-8 border-4 border-blue-200 border-l-blue-600 rounded-full animate-spin"></div>
          <p className="mt-4 text-sm opacity-70">点击或按空格键取消连接</p>
        </div>
      );
    
    default:
      return null;
  }
};