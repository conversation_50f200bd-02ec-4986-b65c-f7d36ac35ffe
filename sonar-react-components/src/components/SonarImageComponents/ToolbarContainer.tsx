import React from 'react';
import { ConnectionStatus } from '../../lib/hooks/useConnectionManager';

// 组件属性接口
export interface ToolbarContainerProps {
  connectionStatus: ConnectionStatus;
  isRecording: boolean;
  showToolbar: boolean;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
  children: React.ReactNode;
}

/**
 * 工具栏容器组件
 * 提供统一的工具栏显示容器，处理显示/隐藏动画
 */
export const ToolbarContainer: React.FC<ToolbarContainerProps> = ({
  connectionStatus,
  isRecording,
  showToolbar,
  onMouseEnter,
  onMouseLeave,
  children,
}) => {
  // 只在连接、暂停或录制状态下显示工具栏
  if (connectionStatus !== "connected" && connectionStatus !== "paused" && !isRecording) {
    return null;
  }

  return (
    <div 
      className="absolute top-0 right-0 w-32 z-30"
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      {/* 触发区域 */}
      <div className="w-full h-16" />
      
      {/* 工具栏 */}
      <div 
        className={`absolute top-0 right-0 transition-transform duration-300 ease-out ${
          showToolbar || isRecording ? 'translate-y-0' : '-translate-y-full'
        }`}
      >
        <div className="bg-black/60 backdrop-blur-sm rounded-bl-lg p-2 flex items-center gap-2">
          {children}
        </div>
      </div>
    </div>
  );
};