import React from 'react';
import { ConnectionStatus } from '../../lib/hooks/useConnectionManager';
import { MeasurementState, MeasurementMode } from '../measurement';

// 组件属性接口
export interface StatusIndicatorsProps {
  // 连接和录制状态
  connectionStatus: ConnectionStatus;
  recordingError: string | null;
  debug: boolean;
  
  // 测量功能状态
  enableMeasurement: boolean;
  measurementState: MeasurementState;
  showMeasurementToolbar: boolean;
  
  // 事件处理函数
  onMeasurementModeChange: (mode: MeasurementMode) => void;
  onClearMeasurements: () => void;
  
  // 样式配置
  themeStyles: {
    statusBar: string;
  };
}

/**
 * 状态指示器组件
 * 显示各种状态提示信息，包括调试信息、测量工具栏、测量结果等
 */
export const StatusIndicators: React.FC<StatusIndicatorsProps> = ({
  connectionStatus,
  recordingError,
  debug,
  enableMeasurement,
  measurementState,
  showMeasurementToolbar,
  onMeasurementModeChange,
  onClearMeasurements,
  themeStyles,
}) => {
  const isConnectedOrPaused = connectionStatus === "connected" || connectionStatus === "paused";

  return (
    <>
      {/* 调试工具栏 - 只在调试模式下显示 */}
      {debug && (
        <div className={`absolute top-0 left-0 right-0 px-2 py-1 text-xs flex justify-center items-center ${themeStyles.statusBar}`}>
          <strong>Debugging Mode</strong>
        </div>
      )}

      {/* 录制错误提示 */}
      {recordingError && (
        <div className="absolute top-16 right-3 z-30 max-w-xs px-3 py-2 text-xs bg-red-900/80 border border-red-600 rounded text-red-200">
          录制错误: {recordingError}
        </div>
      )}

      {/* 测量工具栏 */}
      {enableMeasurement && showMeasurementToolbar && isConnectedOrPaused && (
        <div className="absolute top-12 left-2 bg-black/80 backdrop-blur-sm rounded-lg p-3 text-white z-40">
          <h4 className="text-sm font-semibold mb-2">测量工具</h4>
          <div className="flex flex-col gap-2">
            <button
              onClick={() => onMeasurementModeChange(measurementState.mode === 'point' ? 'none' : 'point')}
              className={`px-3 py-1 rounded text-sm transition-colors ${
                measurementState.mode === 'point' 
                  ? 'bg-blue-600 hover:bg-blue-700' 
                  : 'bg-gray-600 hover:bg-gray-700'
              }`}
            >
              点测量
            </button>
            <button
              onClick={() => onMeasurementModeChange(measurementState.mode === 'distance' ? 'none' : 'distance')}
              className={`px-3 py-1 rounded text-sm transition-colors ${
                measurementState.mode === 'distance' 
                  ? 'bg-blue-600 hover:bg-blue-700' 
                  : 'bg-gray-600 hover:bg-gray-700'
              }`}
            >
              距离测量
            </button>
            <button
              onClick={() => onMeasurementModeChange(measurementState.mode === 'continuous' ? 'none' : 'continuous')}
              className={`px-3 py-1 rounded text-sm transition-colors ${
                measurementState.mode === 'continuous' 
                  ? 'bg-blue-600 hover:bg-blue-700' 
                  : 'bg-gray-600 hover:bg-gray-700'
              }`}
            >
              连续测距
            </button>
            <button
              onClick={onClearMeasurements}
              className="px-3 py-1 rounded text-sm bg-red-600 hover:bg-red-700 transition-colors"
            >
              清除测量
            </button>
          </div>
        </div>
      )}

      {/* 测量结果显示 */}
      {enableMeasurement && measurementState.results.length > 0 && isConnectedOrPaused && (
        <div className="absolute top-12 right-2 bg-black/80 backdrop-blur-sm rounded-lg p-3 text-white text-sm max-w-xs z-50">
          <h4 className="font-semibold mb-2">测量结果</h4>
          {measurementState.results.slice(-3).map((result) => (
            <div key={result.id} className="mb-1 font-mono text-xs">
              {result.description}
            </div>
          ))}
        </div>
      )}

      {/* 测量模式指示器 */}
      {enableMeasurement && measurementState.mode !== 'none' && isConnectedOrPaused && (
        <div className="absolute bottom-2 left-2 bg-blue-600/90 backdrop-blur-sm rounded px-3 py-1 text-white text-sm z-50">
          {measurementState.mode === 'point' && '点击标记测量点'}
          {measurementState.mode === 'distance' && '点击两个点测量距离'}
          {measurementState.mode === 'continuous' && '连续点击测量连接距离'}
          {measurementState.mode === 'angle' && '点击定义角度测量'}
          {measurementState.mode === 'segment' && '点击定义线段'}
        </div>
      )}
    </>
  );
};