import React, { useState, useEffect, useRef, useCallback, useMemo } from "react";
import {
  MJPEGStreamClient,
  SonarMetadata,
  FrameData,
  StreamStatus,
  createImageUrl,
  extractSonarMetadata,
} from "../lib/mjpeg-stream";
import { useVideoRecorder } from "../lib/hooks/useVideoRecorder";
import { Circle, Square, Download, Power, Ruler, CirclePause, Play, Maximize2, Minimize2 } from "lucide-react";
import { MeasurementOverlay, MeasurementState, MeasurementMode } from "./measurement";
import { SonarMeasurementConfig } from "../lib/sonar-geometry";
import { createViewportTransformer } from "../lib/coordinate-transform";

// 叠加层配置接口
interface OverlayConfig {
  showFrameCount?: boolean;
  showTimestamp?: boolean;
  showSonarParams?: boolean;
}

// 缩放模式
type ScaleMode = 'fit' | 'original';

// 缩放配置接口
interface ScaleConfig {
  mode: ScaleMode;
  allowModeToggle?: boolean;
}

// 组件属性定义
interface SonarImageProps {
  sonarIP: string;
  streamEndpoint?: string;
  onError?: (error: Error) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onMetaData?: (metadata: SonarMetadata) => void;
  debug?: boolean;
  theme?: "light" | "dark";
  autoStart?: boolean;
  overlayConfig?: OverlayConfig;
  // 测量功能配置
  measurementConfig?: SonarMeasurementConfig;
  enableMeasurement?: boolean;
  // 缩放功能配置
  scaleConfig?: ScaleConfig;
}

// 默认叠加层配置
const defaultOverlayConfig: OverlayConfig = {
  showFrameCount: true,
  showTimestamp: false,
  showSonarParams: true,
};

// 默认测量配置
const defaultMeasurementConfig: SonarMeasurementConfig = {
  enabled: true,
  originConfig: {
    preset: 'bottom-center',
  },
  sonarAngle: 120,
  sonarDirection: 270,
  showOriginMarker: false,
  showSectorOutline: false,
  coordinateFormat: 'polar',
  precision: {
    distance: 2,
    angle: 1,
  },
};

// 默认缩放配置
const defaultScaleConfig: ScaleConfig = {
  mode: 'fit',
  allowModeToggle: true,
};

/**
 * SonarImage组件 - 用于从Sonar设备获取MJPEG流并显示
 * 处理三种状态: 待机、连接中和已连接(显示)
 * 
 * @param autoStart - 是否在组件初始化时自动开始连接 (默认: false)
 *                   设置为true时，组件加载后立即开始连接流，无需用户点击
 */
const SonarImage: React.FC<SonarImageProps> = ({
  sonarIP,
  streamEndpoint = "/stream",
  onError,
  onConnect,
  onDisconnect,
  onMetaData,
  debug = false,
  theme = "dark",
  autoStart = false,
  overlayConfig,
  measurementConfig,
  enableMeasurement = false,
  scaleConfig,
}) => {
  // 状态定义
  const [connectionStatus, setConnectionStatus] = useState<
    "standby" | "connecting" | "connected" | "paused"
  >("standby");
  const [error, setError] = useState<string | null>(null);
  const [isMouseIdle, setIsMouseIdle] = useState<boolean>(false);
  const [showRecordingToolbar, setShowRecordingToolbar] = useState<boolean>(false);
  const [currentImageSrc, setCurrentImageSrc] = useState<string | null>(null);
  // 暂停状态时保存的最后一帧图像
  const [pausedImageSrc, setPausedImageSrc] = useState<string | null>(null);

  // 测量功能状态
  const [measurementState, setMeasurementState] = useState<MeasurementState>({
    mode: 'none',
    isActive: false,
    points: [],
    results: [],
    isDragging: false,
  });
  const [showMeasurementToolbar, setShowMeasurementToolbar] = useState<boolean>(false);
  const [currentSonarMetadata, setCurrentSonarMetadata] = useState<SonarMetadata["SonarParams"] | null>(null);
  
  // 合并的缩放配置
  const finalScaleConfig = useMemo(() => {
    return { ...defaultScaleConfig, ...scaleConfig };
  }, [scaleConfig]);

  // 缩放功能状态
  const [currentScaleMode, setCurrentScaleMode] = useState<ScaleMode>(() => 
    finalScaleConfig?.mode || defaultScaleConfig.mode
  );
  const [containerSize, setContainerSize] = useState<{width: number, height: number}>({width: 0, height: 0});

  // 引用
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const streamClientRef = useRef<MJPEGStreamClient<SonarMetadata> | null>(null);
  const cleanupRef = useRef<(() => void) | null>(null);
  const mouseIdleTimerRef = useRef<NodeJS.Timeout | null>(null);
  const toolbarHideTimerRef = useRef<NodeJS.Timeout | null>(null);
  const frameCountRef = useRef<number>(0);

  // 录制相关状态
  const [recordedBlob, setRecordedBlob] = useState<Blob | null>(null);
  const videoRecorder = useVideoRecorder({
    mimeType: 'video/mp4',
    videoBitsPerSecond: 2500000, // 2.5 Mbps
    sonarIP,
  });
  const videoRecorderRef = useRef(videoRecorder);

  // 更新 videoRecorder ref
  useEffect(() => {
    videoRecorderRef.current = videoRecorder;
  });

  // 辅助函数：构造API URL
  const streamUrl = useMemo(() => {
    // 检查是否包含协议和端口
    if (sonarIP.startsWith('http://') || sonarIP.startsWith('https://')) {
      return `${sonarIP}${streamEndpoint}`;
    }
    
    // 否则使用默认的 http 协议和端口80
    return `http://${sonarIP}${streamEndpoint}`;
  }, [sonarIP, streamEndpoint]);

  // 格式化录制时间显示
  const formatRecordingTime = useCallback((seconds: number): string => {
    if (seconds < 60) {
      return `${seconds}s`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}m ${remainingSeconds}s`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const remainingSeconds = seconds % 60;
      return `${hours}h ${minutes}m ${remainingSeconds}s`;
    }
  }, []);

  // 将图像绘制到 canvas 上（同步传递声呐数据）
  const drawImageToCanvas = useCallback((
    imageSrc: string,
    currentFrameCount: number,
    sonarMetadata: SonarMetadata["SonarParams"],
    timestamp?: number
  ) => {
    const canvas = canvasRef.current;
    const img = imageRef.current;
    
    if (!canvas || !img) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    img.onload = () => {
      // 设置 canvas 尺寸为图像的实际像素尺寸
      canvas.width = img.naturalWidth;
      canvas.height = img.naturalHeight;
      
      // 清除画布并绘制新图像
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.drawImage(img, 0, 0);
      
      // 根据配置绘制叠加信息
      const config = { ...defaultOverlayConfig, ...overlayConfig };
      
      // 绘制帧数信息
      if (config.showFrameCount) {
        const frameText = `已接收: ${currentFrameCount} 帧`;
        const frameX = 12;
        const frameY = canvas.height - 12;
        
        // 绘制帧数文本
        ctx.save();
        ctx.font = '12px Arial, sans-serif';
        const frameTextMetrics = ctx.measureText(frameText);
        const frameTextWidth = frameTextMetrics.width;
        const frameTextHeight = 12;
        const framePadding = 4;
        
        // 绘制背景
        ctx.fillStyle = theme === 'dark' ? 'rgba(0, 0, 0, 0.8)' : 'rgba(31, 41, 55, 0.75)';
        ctx.fillRect(frameX - framePadding, frameY - frameTextHeight - framePadding, frameTextWidth + framePadding * 2, frameTextHeight + framePadding * 2);
        
        // 绘制文本
        ctx.fillStyle = theme === 'dark' ? '#d1d5db' : '#374151';
        ctx.fillText(frameText, frameX, frameY);
        ctx.restore();
      }
        
      // 绘制声呐信息
      if ((config.showTimestamp || config.showSonarParams) && sonarMetadata && Object.keys(sonarMetadata).length > 0) {
          const lines: string[] = [];
          
          // 时间戳信息
          if (config.showTimestamp && timestamp) {
            const date = new Date(timestamp);
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            const seconds = date.getSeconds().toString().padStart(2, '0');
            const milliseconds = date.getMilliseconds().toString().padStart(3, '0');
            lines.push(`时间: ${hours}:${minutes}:${seconds}.${milliseconds}`);
          }
          
          // 声呐参数信息
          if (config.showSonarParams) {
            if (sonarMetadata.range !== undefined) {
              lines.push(`量程: ${sonarMetadata.range.toFixed(2)}米`);
            }
            if (sonarMetadata.pingPeriod) {
              lines.push(`发射周期: ${Math.round(sonarMetadata.pingPeriod)} ms`);
            }
            if (sonarMetadata.beamNum) {
              lines.push(`波束数: ${sonarMetadata.beamNum}`);
            }
            if (sonarMetadata.velocity) {
              lines.push(`声速: ${sonarMetadata.velocity} m/s`);
            }
            if (sonarMetadata.gain !== undefined) {
              lines.push(`增益: ${sonarMetadata.gain}`);
            }
          }
          
          if (lines.length > 0) {
            const lineHeight = 16;
            const padding = 8;
            const rightMargin = 8;
            const bottomMargin = 40;
            
            const startY = canvas.height - bottomMargin - (lines.length - 1) * lineHeight;
            
            lines.forEach((line, index) => {
              const textMetrics = ctx.measureText(line);
              const x = canvas.width - textMetrics.width - rightMargin - padding;
              const y = startY + index * lineHeight;
              
              // 绘制声呐信息文本
              ctx.save();
              ctx.font = '12px Arial, sans-serif';
              const lineMetrics = ctx.measureText(line);
              const lineWidth = lineMetrics.width;
              const textHeight = 12;
              const linePadding = 4;
              
              // 绘制背景
              ctx.fillStyle = theme === 'dark' ? 'rgba(0, 0, 0, 0.8)' : 'rgba(31, 41, 55, 0.75)';
              ctx.fillRect(x - linePadding, y - textHeight - linePadding, lineWidth + linePadding * 2, textHeight + linePadding * 2);
              
              // 绘制文本
              ctx.fillStyle = theme === 'dark' ? '#d1d5db' : '#374151';
              ctx.fillText(line, x, y);
              ctx.restore();
            });
          }
        }
      
      // 释放 URL 资源
      URL.revokeObjectURL(imageSrc);
    };
    
    img.src = imageSrc;
  }, [theme, overlayConfig]);

  // 缩放功能控制函数
  const handleScaleModeChange = useCallback(() => {
    if (finalScaleConfig.allowModeToggle) {
      setCurrentScaleMode(prev => prev === 'fit' ? 'original' : 'fit');
    }
  }, [finalScaleConfig.allowModeToggle]);

  // 测量功能控制函数
  const handleMeasurementModeChange = useCallback((mode: MeasurementMode) => {
    setMeasurementState(prev => ({
      ...prev,
      mode,
      isActive: mode !== 'none',
      points: mode === 'none' ? [] : prev.points,
      results: mode === 'none' ? [] : prev.results,
    }));
  }, []);

  const handleClearMeasurements = useCallback(() => {
    setMeasurementState(prev => ({
      ...prev,
      points: [],
      results: [],
    }));
  }, []);

  const handleToggleMeasurementToolbar = useCallback(() => {
    setShowMeasurementToolbar(prev => !prev);
  }, []);

  // 录制控制函数
  const handleStartRecording = useCallback(async () => {
    const canvas = canvasRef.current;
    if (!canvas) {
      console.error('Canvas 元素不可用');
      return;
    }
    
    // 调试信息
    console.log('Canvas 信息:', {
      width: canvas.width,
      height: canvas.height,
      clientWidth: canvas.clientWidth,
      clientHeight: canvas.clientHeight
    });
    
    // 检查 canvas 是否有内容
    const ctx = canvas.getContext('2d');
    if (ctx) {
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const hasContent = imageData.data.some(value => value !== 0);
      console.log('Canvas 是否有内容:', hasContent);
    }
    
    const success = await videoRecorder.startRecording(canvas);
    if (success) {
      console.log('录制已开始');
    }
  }, [videoRecorder]);

  const handleStopRecording = useCallback(async () => {
    const blob = await videoRecorder.stopRecording();
    if (blob) {
      setRecordedBlob(blob);
      console.log('录制已停止，文件大小:', blob.size);
    }
  }, [videoRecorder]);

  const handleDownloadRecording = useCallback(async () => {
    if (recordedBlob) {
      videoRecorder.downloadRecording(recordedBlob);
      setRecordedBlob(null); // 下载后清除
    }
  }, [recordedBlob, videoRecorder]);

  // 鼠标移动处理函数
  const handleMouseMove = useCallback(() => {
    setIsMouseIdle(false);
    
    // 清除之前的定时器
    if (mouseIdleTimerRef.current) {
      clearTimeout(mouseIdleTimerRef.current);
    }
    
    // 设置新的定时器，2秒后隐藏鼠标
    mouseIdleTimerRef.current = setTimeout(() => {
      setIsMouseIdle(true);
    }, 2000);
  }, []);

  // 工具栏显示/隐藏处理函数
  const handleToolbarMouseEnter = useCallback(() => {
    setShowRecordingToolbar(true);
    
    // 清除隐藏定时器
    if (toolbarHideTimerRef.current) {
      clearTimeout(toolbarHideTimerRef.current);
      toolbarHideTimerRef.current = null;
    }
  }, []);

  const handleToolbarMouseLeave = useCallback(() => {
    // 如果正在录制，不自动隐藏工具栏
    if (videoRecorder.isRecording) {
      return;
    }
    
    // 延迟隐藏工具栏
    toolbarHideTimerRef.current = setTimeout(() => {
      setShowRecordingToolbar(false);
    }, 1000);
  }, [videoRecorder.isRecording]);

  // 暂停控制函数
  const handlePause = useCallback(() => {
    if (connectionStatus === "connected" && currentImageSrc) {
      // 保存当前图像用于暂停显示
      setPausedImageSrc(currentImageSrc);
      // 断开底层连接
      if (streamClientRef.current) {
        streamClientRef.current.stop();
      }
      // 设置为暂停状态
      setConnectionStatus("paused");
      console.log('📸 [SonarImage] 暂停连接，保存最后一帧图像');
    }
  }, [connectionStatus, currentImageSrc]);

  const handleResumeFromPause = useCallback(() => {
    if (connectionStatus === "paused") {
      // 清除暂停图像
      setPausedImageSrc(null);
      // 重新连接
      if (streamClientRef.current) {
        streamClientRef.current.start();
      }
      console.log('▶️ [SonarImage] 从暂停状态恢复连接');
    }
  }, [connectionStatus]);

  const handleStopFromPause = useCallback(() => {
    if (connectionStatus === "paused") {
      // 清除暂停图像
      setPausedImageSrc(null);
      // 设置为待机状态
      setConnectionStatus("standby");
      console.log('⏹️ [SonarImage] 从暂停状态返回待机');
    }
  }, [connectionStatus]);

  // 点击图像区域时切换连接状态
  const handleImageClick = useCallback(() => {
    if (streamClientRef.current) {
      const currentStatus = streamClientRef.current.status;
      if (currentStatus === StreamStatus.CONNECTING) {
        // 连接中状态时，直接停止
        streamClientRef.current.stop();
      } else {
        // 其他状态时，切换连接状态
        streamClientRef.current.toggle();
      }
    }
  }, []);

  // 容器点击处理函数 - 处理连接状态和测量模式
  const handleContainerClick = useCallback(() => {
    // 如果未连接且未暂停，直接处理连接逻辑
    if (connectionStatus !== "connected" && connectionStatus !== "paused") {
      handleImageClick();
      return;
    }
    
    // 如果已连接或暂停且测量模式不活跃，则不处理（让MeasurementOverlay处理）
    if (enableMeasurement && measurementState.mode !== 'none') {
      return;
    }
    
    // 其他情况不处理点击（已连接或暂停状态）
  }, [connectionStatus, enableMeasurement, measurementState.mode, handleImageClick]);

  // 键盘事件处理函数
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (event.code === 'Space') {
      event.preventDefault(); // 阻止默认的滚动行为
      handleImageClick();
    }
  }, [handleImageClick]);

  // 使用 useRef 保持回调函数的稳定引用
  const callbacksRef = useRef({
    onConnect,
    onDisconnect,
    onError,
    onMetaData,
    drawImageToCanvas
  });

  // 更新回调函数引用
  useEffect(() => {
    callbacksRef.current = {
      onConnect,
      onDisconnect,
      onError,
      onMetaData,
      drawImageToCanvas
    };
  }, [onConnect, onDisconnect, onError, onMetaData, drawImageToCanvas]);

  // 客户端回调函数 - 使用稳定的引用
  const clientCallbacks = useMemo(() => ({
    onConnect: () => {
      if (callbacksRef.current.onConnect) callbacksRef.current.onConnect();
    },
    onDisconnect: () => {
      if (callbacksRef.current.onDisconnect) callbacksRef.current.onDisconnect();
    },
    onError: (err: Error) => {
      if (callbacksRef.current.onError) callbacksRef.current.onError(err);
    },
    onFrame: (frameData: FrameData<SonarMetadata>) => {
      const url = createImageUrl(frameData.frame);
      setCurrentImageSrc(url);
      
      // 增加帧数并获取当前帧数
      frameCountRef.current += 1;
      const currentFrameCount = frameCountRef.current;
      
      // 获取当前帧的时间戳
      const currentTimestamp = frameData.metadata.SonarParams?.timestamp || Date.now();
      
      // 保存当前声呐元数据供测量功能使用
      if (frameData.metadata.SonarParams) {
        setCurrentSonarMetadata(frameData.metadata.SonarParams);
      }
      
      // 同步传递声呐数据到 canvas 绘制函数
      callbacksRef.current.drawImageToCanvas(
        url,
        currentFrameCount,
        frameData.metadata.SonarParams || {},
        currentTimestamp
      );
      
    },
    onMetadata: (metadata: SonarMetadata) => {
      if (metadata.SonarParams) {
        if (callbacksRef.current.onMetaData) callbacksRef.current.onMetaData(metadata);
      }
    },
  }), []); // 空依赖数组，确保稳定引用

  // 创建流客户端（只创建一次）
  useEffect(() => {
    const componentId = Math.random().toString(36).substring(2, 11);
    console.log(`🏗️ [SonarImage-${componentId}] useEffect 执行 - 创建客户端`);
    
    if (streamClientRef.current) {
      console.log(`⚠️ [SonarImage-${componentId}] 客户端已存在，跳过创建`);
      return; // 已经存在客户端，不需要重新创建
    }

    console.log(`🆕 [SonarImage-${componentId}] 创建新的 MJPEGStreamClient，autoStart=false`);
    const client = new MJPEGStreamClient<SonarMetadata>(
      {
        streamUrl,
        debug,
        metadataExtractor: extractSonarMetadata,
        autoStart: false, // 手动控制启动
      },
      clientCallbacks
    );

    streamClientRef.current = client;

    // 订阅客户端状态变化
    const unsubscribe = client.subscribe((status, errorMsg) => {
      // 将客户端状态映射到组件状态
      const componentStatus = status === StreamStatus.STANDBY ? "standby" 
        : status === StreamStatus.CONNECTING ? "connecting" 
        : "connected";
      
      setConnectionStatus(componentStatus);
      setError(errorMsg || null);
      
      // 重置图像源
      if (componentStatus !== "connected") {
        setCurrentImageSrc(null);
      }
    });

    // 保存清理函数
    cleanupRef.current = () => {
      console.log(`🧹 [SonarImage-${componentId}] 清理客户端`);
      unsubscribe();
      client.stop();
      // 清理鼠标空闲定时器
      if (mouseIdleTimerRef.current) {
        clearTimeout(mouseIdleTimerRef.current);
        mouseIdleTimerRef.current = null;
      }
      // 清理工具栏隐藏定时器
      if (toolbarHideTimerRef.current) {
        clearTimeout(toolbarHideTimerRef.current);
        toolbarHideTimerRef.current = null;
      }
    };

    // 组件卸载时清理
    return () => {
      console.log(`🔄 [SonarImage-${componentId}] useEffect 清理回调执行`);
      if (cleanupRef.current) {
        cleanupRef.current();
        cleanupRef.current = null;
      }
      streamClientRef.current = null;
    };
  }, [clientCallbacks, debug, streamUrl]); // 包含所有依赖项，但clientCallbacks现在是稳定的

  // 更新客户端配置
  useEffect(() => {
    console.log(`⚙️ [SonarImage] 配置更新 useEffect 执行，url=${streamUrl}, debug=${debug}`);
    if (streamClientRef.current) {
      console.log(`🔧 [SonarImage] 更新客户端配置`);
      streamClientRef.current.updateOptions({
        streamUrl,
        debug,
        metadataExtractor: extractSonarMetadata,
      });
    }
  }, [streamUrl, debug]);

  // 控制连接启动/停止
  useEffect(() => {
    console.log(`🎛️ [SonarImage] autoStart 控制 useEffect 执行，autoStart=${autoStart}`);
    if (streamClientRef.current && autoStart) {
      console.log(`▶️ [SonarImage] 启动客户端连接`);
      streamClientRef.current.start();
    }
  }, [autoStart]);

  // 录制状态变化时管理工具栏显示
  useEffect(() => {
    if (videoRecorder.isRecording) {
      // 开始录制时显示工具栏
      setShowRecordingToolbar(true);
      // 清除隐藏定时器
      if (toolbarHideTimerRef.current) {
        clearTimeout(toolbarHideTimerRef.current);
        toolbarHideTimerRef.current = null;
      }
    }
  }, [videoRecorder.isRecording]);

  // 监控连接状态变化，控制录制暂停/恢复
  useEffect(() => {
    if (videoRecorder.isRecording) {
      if (connectionStatus === "connected") {
        // 连接恢复时恢复录制计时器
        videoRecorder.resumeTimer();
      } else {
        // 连接断开时暂停录制计时器
        videoRecorder.pauseTimer();
      }
    }
  }, [connectionStatus, videoRecorder]);

  // 添加键盘事件监听器
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);
  
  // 监听容器尺寸变化
  useEffect(() => {
    updateContainerSize();
    
    const container = containerRef.current;
    if (!container) return;
    
    const resizeObserver = new ResizeObserver(() => {
      updateContainerSize();
    });
    resizeObserver.observe(container);
    
    return () => {
      resizeObserver.disconnect();
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps


  // 获取主题相关的样式
  const themeStyles = useMemo(() => {
    if (theme === "light") {
      return {
        container: "bg-black border-gray-300",
        standby: "bg-gray-50 text-gray-700",
        connecting: "bg-gray-100 text-blue-700",
        connected: "bg-black",
        statusBar: "bg-gray-800 bg-opacity-75 text-white",
        infoPanel: "bg-gray-800 bg-opacity-75 text-white",
        error: "text-red-700",
      };
    } else {
      return {
        container: "bg-black border-gray-700",
        standby: "bg-black text-gray-300",
        connecting: "bg-gray-900 text-blue-400",
        connected: "bg-black",
        statusBar: "bg-black bg-opacity-80 text-gray-200",
        infoPanel: "bg-black bg-opacity-80 text-gray-200",
        error: "text-red-400",
      };
    }
  }, [theme]);

  // 合并的测量配置
  const finalMeasurementConfig = useMemo(() => {
    return { ...defaultMeasurementConfig, ...measurementConfig };
  }, [measurementConfig]);
  
  // 容器尺寸监听
  const containerRef = useRef<HTMLDivElement>(null);
  
  // 容器尺寸更新
  const updateContainerSize = useCallback(() => {
    const container = containerRef.current;
    if (container) {
      const rect = container.getBoundingClientRect();
      setContainerSize({
        width: rect.width,
        height: rect.height,
      });
    }
  }, []);

  // 计算当前图像尺寸和量程
  const [imageMetrics, setImageMetrics] = useState<{width: number, height: number, range: number} | null>(null);
  
  // 缩放计算逻辑 - 简化版
  const scaleCalculation = useMemo(() => {
    if (!imageMetrics || containerSize.width === 0 || containerSize.height === 0) {
      return {
        scale: 1,
        transformOrigin: 'center center',
      };
    }
    
    const { width: imageWidth, height: imageHeight } = imageMetrics;
    const { width: containerWidth, height: containerHeight } = containerSize;
    
    // 计算可能的缩放值
    const scaleX = containerWidth / imageWidth;
    const scaleY = containerHeight / imageHeight;
    
    if (currentScaleMode === 'fit') {
      // 适应模式：自动缩放以适应容器大小，完整显示所有内容
      const scale = Math.min(scaleX, scaleY); // 使用较小的缩放值确保完整显示
      
      return {
        scale,
        transformOrigin: 'center center',
      };
    } else {
      // 原始模式：保持图像1:1像素对应，不放大但可缩小以适应容器
      const scale = Math.min(1, Math.min(scaleX, scaleY)); // 不超过原始尺寸，也不超过容器
      
      return {
        scale,
        transformOrigin: 'center center',
      };
    }
  }, [imageMetrics, containerSize, currentScaleMode]);

  // 创建坐标转换器
  const coordinateTransformer = useMemo(() => {
    if (!imageMetrics) {
      return undefined;
    }

    return createViewportTransformer({
      scale: scaleCalculation.scale,
      transformOrigin: scaleCalculation.transformOrigin,
    });
  }, [scaleCalculation, imageMetrics]);

  // 监听canvas尺寸变化
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || !currentSonarMetadata) {
      setImageMetrics(null);
      return;
    }

    const updateMetrics = () => {
      setImageMetrics({
        width: canvas.width || 640,
        height: canvas.height || 480,
        range: currentSonarMetadata.range || 50,
      });
    };

    // 初始更新
    updateMetrics();

    // 使用ResizeObserver监听canvas尺寸变化
    const resizeObserver = new ResizeObserver(() => {
      updateMetrics();
    });
    
    resizeObserver.observe(canvas);

    return () => {
      resizeObserver.disconnect();
    };
  }, [currentSonarMetadata]);

  // 根据连接状态渲染不同的内容
  const renderContent = useCallback(() => {
    switch (connectionStatus) {
      case "standby":
        return (
          <div className={`flex flex-col justify-center items-center w-full h-full text-center p-5 ${themeStyles.standby}`}>
            <p>点击连接MJPEG流</p>
            <p className="mt-2 text-sm opacity-70">或按空格键</p>
            {error && <p className={`mt-2 text-sm ${themeStyles.error}`}>{error}</p>}
          </div>
        );
      case "connecting":
        return (
          <div className={`flex flex-col justify-center items-center w-full h-full text-center p-5 ${themeStyles.connecting}`}>
            <p>正在连接...</p>
            <div className="mt-4 w-8 h-8 border-4 border-blue-200 border-l-blue-600 rounded-full animate-spin"></div>
            <p className="mt-4 text-sm opacity-70">点击或按空格键取消连接</p>
          </div>
        );
      case "connected":
        return currentImageSrc ? (
          <div className="relative w-full h-full">
            {/* 隐藏的 img 元素用于加载图像 */}
            <img
              ref={imageRef}
              style={{ display: 'none' }}
              alt="Sonar MJPEG Stream"
              onError={(e) => {
                console.error("图像加载错误:", e);
                if (debug) {
                  const img = e.target as HTMLImageElement;
                  console.log("图像URL:", img.src);
                  console.log("图像naturalWidth:", img.naturalWidth);
                  console.log("图像naturalHeight:", img.naturalHeight);
                }
              }}
            />
            {/* 外部容器 - 负责居中显示 */}
            <div className="flex items-center justify-center w-full h-full">
              {/* 统一容器 - 使用缩放后的实际显示尺寸 */}
              <div 
                className="relative"
                style={{
                  width: `${(imageMetrics?.width || 0) * scaleCalculation.scale}px`,
                  height: `${(imageMetrics?.height || 0) * scaleCalculation.scale}px`,
                  display: imageMetrics ? 'block' : 'none'
                }}
              >
                {/* 内层容器 - 处理缩放变换 */}
                <div
                  style={{
                    width: `${imageMetrics?.width || 0}px`,
                    height: `${imageMetrics?.height || 0}px`,
                    transform: `scale(${scaleCalculation.scale})`,
                    transformOrigin: 'top left',
                  }}
                >
                  {/* Canvas层 */}
                  <canvas
                    ref={canvasRef}
                    className="absolute inset-0"
                    style={{
                      width: `${imageMetrics?.width || 0}px`,
                      height: `${imageMetrics?.height || 0}px`,
                    }}
                  />
                  
                  {/* 测量覆盖层 - 使用Canvas相同的像素尺寸 */}
                  {enableMeasurement && connectionStatus === "connected" && imageMetrics && (
                    <MeasurementOverlay
                      enabled={enableMeasurement}
                      measurementState={measurementState}
                      onMeasurementStateChange={setMeasurementState}
                      sonarConfig={finalMeasurementConfig}
                      imageWidth={imageMetrics?.width || 0}
                      imageHeight={imageMetrics?.height || 0}
                      range={imageMetrics.range}
                      coordinateTransformer={coordinateTransformer}
                      className="absolute inset-0"
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex flex-col justify-center items-center w-full h-full text-center p-5 bg-black text-gray-300">
            <p>等待图像数据...</p>
            <div className="mt-4 w-8 h-8 border-4 border-blue-200 border-l-blue-600 rounded-full animate-spin"></div>
          </div>
        );
      case "paused":
        return pausedImageSrc ? (
          <div className="relative w-full h-full">
            {/* 隐藏的 img 元素用于加载暂停的图像 */}
            <img
              ref={imageRef}
              style={{ display: 'none' }}
              alt="Paused Sonar Image"
              src={pausedImageSrc}
            />
            {/* 外部容器 - 负责居中显示 */}
            <div className="flex items-center justify-center w-full h-full">
              {/* 统一容器 - 使用缩放后的实际显示尺寸 */}
              <div 
                className="relative"
                style={{
                  width: `${(imageMetrics?.width || 0) * scaleCalculation.scale}px`,
                  height: `${(imageMetrics?.height || 0) * scaleCalculation.scale}px`,
                  display: imageMetrics ? 'block' : 'none'
                }}
              >
                {/* 内层容器 - 处理缩放变换 */}
                <div
                  style={{
                    width: `${imageMetrics?.width || 0}px`,
                    height: `${imageMetrics?.height || 0}px`,
                    transform: `scale(${scaleCalculation.scale})`,
                    transformOrigin: 'top left',
                  }}
                >
                  {/* Canvas层 */}
                  <canvas
                    ref={canvasRef}
                    className="absolute inset-0"
                    style={{
                      width: `${imageMetrics?.width || 0}px`,
                      height: `${imageMetrics?.height || 0}px`,
                    }}
                  />
                  
                  {/* 测量覆盖层 - 暂停状态下仍可使用 */}
                  {enableMeasurement && imageMetrics && (
                    <MeasurementOverlay
                      enabled={enableMeasurement}
                      measurementState={measurementState}
                      onMeasurementStateChange={setMeasurementState}
                      sonarConfig={finalMeasurementConfig}
                      imageWidth={imageMetrics?.width || 0}
                      imageHeight={imageMetrics?.height || 0}
                      range={imageMetrics.range}
                      coordinateTransformer={coordinateTransformer}
                      className="absolute inset-0"
                    />
                  )}
                </div>
              </div>
            </div>
            
            {/* 暂停状态提示 */}
            <div className="absolute top-2 left-2 bg-yellow-600/80 backdrop-blur-sm rounded px-2 py-1 text-xs text-white">
              已暂停
            </div>
          </div>
        ) : (
          <div className="flex flex-col justify-center items-center w-full h-full text-center p-5 bg-black text-gray-300">
            <p>暂停状态，无图像数据</p>
          </div>
        );
      default:
        return null;
    }
  }, [connectionStatus, error, themeStyles, debug, currentImageSrc, pausedImageSrc, enableMeasurement, finalMeasurementConfig, imageMetrics, measurementState, scaleCalculation, coordinateTransformer]);

  // 渲染声呐参数信息

  return (
    <div
      ref={containerRef}
      className={`relative overflow-hidden flex justify-center items-center transition-all duration-300 ease-in-out w-full h-full ${themeStyles.container} ${
        connectionStatus === "connected" ? themeStyles.connected : ""
      } ${isMouseIdle ? "cursor-none" : ""}`}
      onClick={handleContainerClick}
      onMouseMove={handleMouseMove}
    >
      {renderContent()}
      {(connectionStatus === "connected" || connectionStatus === "paused" || videoRecorder.isRecording) && (
        <>
          {/* 统一录制工具栏区域（包含触发区域和工具栏） */}
          {videoRecorder.isSupported && (
            <div 
              className="absolute top-0 right-0 w-32 z-30"
              onMouseEnter={handleToolbarMouseEnter}
              onMouseLeave={handleToolbarMouseLeave}
            >
              {/* 触发区域 */}
              <div className="w-full h-16" />
              
              {/* 工具栏 */}
              <div 
                className={`absolute top-0 right-0 transition-transform duration-300 ease-out ${
                  showRecordingToolbar || videoRecorder.isRecording ? 'translate-y-0' : '-translate-y-full'
                }`}
              >
                <div className="bg-black/60 backdrop-blur-sm rounded-bl-lg p-2 flex items-center gap-2">
                {/* 连接控制按钮 */}
                {connectionStatus === "connected" && (
                  <>
                    {/* 暂停按钮 */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        handlePause();
                      }}
                      className="bg-yellow-600 hover:bg-yellow-700 text-white border border-yellow-500 rounded p-2 transition-colors"
                      title="暂停"
                    >
                      <CirclePause size={16} />
                    </button>
                    {/* 断开连接按钮 */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        handleImageClick();
                      }}
                      className="bg-gray-600 hover:bg-gray-700 text-white border border-gray-500 rounded p-2 transition-colors"
                      title="断开连接"
                    >
                      <Power size={16} />
                    </button>
                  </>
                )}

                {connectionStatus === "paused" && (
                  <>
                    {/* 恢复按钮 */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        handleResumeFromPause();
                      }}
                      className="bg-green-600 hover:bg-green-700 text-white border border-green-500 rounded p-2 transition-colors"
                      title="恢复连接"
                    >
                      <Play size={16} />
                    </button>
                    {/* 停止按钮 */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        handleStopFromPause();
                      }}
                      className="bg-gray-600 hover:bg-gray-700 text-white border border-gray-500 rounded p-2 transition-colors"
                      title="停止"
                    >
                      <Power size={16} />
                    </button>
                  </>
                )}

                {/* 缩放模式切换按钮 */}
                {finalScaleConfig.allowModeToggle && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      handleScaleModeChange();
                    }}
                    className="bg-gray-600 hover:bg-gray-700 text-white border border-gray-500 rounded p-2 transition-colors"
                    title={`切换到${currentScaleMode === 'fit' ? '原始尺寸' : '适应'}模式`}
                  >
                    {currentScaleMode === 'fit' ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
                  </button>
                )}

                {/* 测量工具按钮 */}
                {enableMeasurement && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      handleToggleMeasurementToolbar();
                    }}
                    className={`border rounded p-2 transition-colors ${
                      showMeasurementToolbar 
                        ? 'bg-blue-600 hover:bg-blue-700 text-white border-blue-500' 
                        : 'bg-gray-600 hover:bg-gray-700 text-white border-gray-500'
                    }`}
                    title="测量工具"
                  >
                    <Ruler size={16} />
                  </button>
                )}
                
                {/* 录制按钮组 */}
                {!videoRecorder.isRecording ? (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      handleStartRecording();
                    }}
                    className="bg-red-600 hover:bg-red-700 text-white border border-red-500 rounded p-2 transition-colors"
                    title="开始录制"
                  >
                    <Circle size={16} />
                  </button>
                ) : (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      handleStopRecording();
                    }}
                    className={`bg-red-700 text-white border border-red-600 rounded p-2 transition-colors ${
                      videoRecorder.isPaused ? '' : 'animate-pulse'
                    }`}
                    title={`停止录制 (${formatRecordingTime(videoRecorder.duration)})`}
                  >
                    <Square size={16} />
                  </button>
                )}
                
                {recordedBlob && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      handleDownloadRecording();
                    }}
                    className="bg-green-600 hover:bg-green-700 text-white border border-green-500 rounded p-2 transition-colors"
                    title="下载录制文件"
                  >
                    <Download size={16} />
                  </button>
                )}

                {/* 录制状态指示器 */}
                {videoRecorder.isRecording && (
                  <div className="flex items-center gap-2 ml-2 px-2 py-1 text-xs text-white rounded bg-red-900/50 whitespace-nowrap">
                    <div className={`w-2 h-2 bg-red-500 rounded-full ${
                      videoRecorder.isPaused ? '' : 'animate-pulse'
                    }`}></div>
                    <span>
                      {videoRecorder.isPaused ? '录制暂停' : '录制中'} {formatRecordingTime(videoRecorder.duration)}
                    </span>
                  </div>
                )}
                </div>
              </div>
            </div>
          )}

          {/* 录制错误提示 */}
          {videoRecorder.error && (
            <div className="absolute top-16 right-3 z-30 max-w-xs px-3 py-2 text-xs bg-red-900/80 border border-red-600 rounded text-red-200">
              录制错误: {videoRecorder.error}
            </div>
          )}

        </>
      )}

      {/* 调试工具栏 - 只在调试模式下显示 */}
      {debug && (
        <div className={`absolute top-0 left-0 right-0 px-2 py-1 text-xs flex justify-center items-center ${themeStyles.statusBar}`}>
          <strong>Debugging Mode</strong>
        </div>
      )}


      {/* 测量工具栏 */}
      {enableMeasurement && showMeasurementToolbar && (connectionStatus === "connected" || connectionStatus === "paused") && (
        <div className="absolute top-12 left-2 bg-black/80 backdrop-blur-sm rounded-lg p-3 text-white z-40">
          <h4 className="text-sm font-semibold mb-2">测量工具</h4>
          <div className="flex flex-col gap-2">
            <button
              onClick={() => handleMeasurementModeChange(measurementState.mode === 'point' ? 'none' : 'point')}
              className={`px-3 py-1 rounded text-sm transition-colors ${
                measurementState.mode === 'point' 
                  ? 'bg-blue-600 hover:bg-blue-700' 
                  : 'bg-gray-600 hover:bg-gray-700'
              }`}
            >
              点测量
            </button>
            <button
              onClick={() => handleMeasurementModeChange(measurementState.mode === 'distance' ? 'none' : 'distance')}
              className={`px-3 py-1 rounded text-sm transition-colors ${
                measurementState.mode === 'distance' 
                  ? 'bg-blue-600 hover:bg-blue-700' 
                  : 'bg-gray-600 hover:bg-gray-700'
              }`}
            >
              距离测量
            </button>
            <button
              onClick={() => handleMeasurementModeChange(measurementState.mode === 'continuous' ? 'none' : 'continuous')}
              className={`px-3 py-1 rounded text-sm transition-colors ${
                measurementState.mode === 'continuous' 
                  ? 'bg-blue-600 hover:bg-blue-700' 
                  : 'bg-gray-600 hover:bg-gray-700'
              }`}
            >
              连续测距
            </button>
            <button
              onClick={handleClearMeasurements}
              className="px-3 py-1 rounded text-sm bg-red-600 hover:bg-red-700 transition-colors"
            >
              清除测量
            </button>
          </div>
        </div>
      )}

      {/* 测量结果显示 */}
      {enableMeasurement && measurementState.results.length > 0 && (connectionStatus === "connected" || connectionStatus === "paused") && (
        <div className="absolute top-12 right-2 bg-black/80 backdrop-blur-sm rounded-lg p-3 text-white text-sm max-w-xs z-50">
          <h4 className="font-semibold mb-2">测量结果</h4>
          {measurementState.results.slice(-3).map((result) => (
            <div key={result.id} className="mb-1 font-mono text-xs">
              {result.description}
            </div>
          ))}
        </div>
      )}

      {/* 模式指示器 */}
      {enableMeasurement && measurementState.mode !== 'none' && (connectionStatus === "connected" || connectionStatus === "paused") && (
        <div className="absolute bottom-2 left-2 bg-blue-600/90 backdrop-blur-sm rounded px-3 py-1 text-white text-sm z-50">
          {measurementState.mode === 'point' && '点击标记测量点'}
          {measurementState.mode === 'distance' && '点击两个点测量距离'}
          {measurementState.mode === 'continuous' && '连续点击测量连接距离'}
          {measurementState.mode === 'angle' && '点击定义角度测量'}
          {measurementState.mode === 'segment' && '点击定义线段'}
        </div>
      )}
    </div>
  );
};

export default SonarImage;